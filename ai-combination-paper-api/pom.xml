<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.iflytek.edu</groupId>
    <artifactId>ai-combination-paper-api</artifactId>
    <version>${ai-combination-papper-api.version}</version>

    <properties>
        <maven.compiler.source>8</maven.compiler.source>
        <maven.compiler.target>8</maven.compiler.target>
        <ai-combination-papper-api.version>1.0.3-SNAPSHOT</ai-combination-papper-api.version>
    </properties>

    <repositories>
        <repository>
            <id>mvn-repo</id>
            <name>mvn-repo</name>
            <url>https://depend.iflytek.com/artifactory/mvn-repo/</url>
            <layout>default</layout>
        </repository>
    </repositories>

    <dependencies>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <version>1.18.24</version>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-validation</artifactId>
            <version>2.7.8</version>
        </dependency>
    </dependencies>

    <!--指定依赖库新域名，skynet-framework升级后可删除-->
    <distributionManagement>
        <repository>
            <id>mvn-releases</id>
            <name>iflytek-nexus</name>
            <url>https://depend.iflytek.com/artifactory/ebg-mvn-release-private</url>
        </repository>
        <snapshotRepository>
            <id>mvn-snapshots</id>
            <name>iflytek-snapshots</name>
            <url>https://depend.iflytek.com/artifactory/ebg-mvn-snapshot-private</url>
        </snapshotRepository>
    </distributionManagement>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <configuration>
                    <source>1.8</source>
                    <target>1.8</target>
                </configuration>
            </plugin>

            <!-- Source attach plugin -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-source-plugin</artifactId>
                <executions>
                    <execution>
                        <id>attach-sources</id>
                        <goals>
                            <goal>jar</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>
</project>