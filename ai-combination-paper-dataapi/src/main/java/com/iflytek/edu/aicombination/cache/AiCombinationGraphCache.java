package com.iflytek.edu.aicombination.cache;

import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSONObject;
import com.iflytek.edu.aicombination.dto.QueryParam;
import com.iflytek.skylab.core.constant.GraphVertexType;
import com.iflytek.skylab.core.dataapi.data.GraphData;
import com.iflytek.skylab.core.dataapi.data.GraphLabelQuery;
import com.iflytek.skylab.core.dataapi.data.SubGraphQuery;
import com.iflytek.skylab.core.dataapi.service.GraphService;
import com.iflytek.skylab.core.dataapi.service.cache.LocalFileGraphCache;
import lombok.extern.slf4j.Slf4j;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.nio.ByteBuffer;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.UUID;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

import static com.iflytek.skylab.core.constant.GraphVertexType.*;


/**
 * AI组卷二级缓存加载,提供引擎四个查询子图的接口
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class AiCombinationGraphCache {

    @Autowired
    @Qualifier("localGraphService")
    private GraphService localGraphService;

    /**
     * 图谱版本
     */
    @Value("${ai.combination.graph-version}")
    private String graphVersion;

    @Value("${ai.combination.phase.subject}")
    private List<String> phaseSubjects;

    @Autowired
    private GraphAddFileCache graphAddFileCache;


    @Resource
    private LocalFileGraphCache localFileGraphCache;

    /**
     * 教材学科、学段到书本信息
     */
    protected static final ConcurrentHashMap<String, ConcurrentHashMap<String, GraphData>> PHASE_SUBJECT_PRESS_BOOK_CACHE = new ConcurrentHashMap<>();


    /**
     * 书本下各级目录的锚点、考点子图
     */
    protected static final ConcurrentHashMap<String, GraphData> BOOK_CACHE = new ConcurrentHashMap<>();

    /**
     * 锚点到题
     */
    protected static final ConcurrentHashMap<String, GraphData> ANCHOR_POINT_TO_TOPIC_CACHE = new ConcurrentHashMap<>();

    /**
     * 考点到题
     */
    protected static final ConcurrentHashMap<String, GraphData> CHECK_POINT_TO_TOPIC_CACHE = new ConcurrentHashMap<>();


    @PostConstruct
    public void initAiCombinationGraphCache() {
        try {
            //循环等待缓存完成
            while (!graphAddFileCache.isUpdateCacheFlag()) {
                Thread.sleep(5000);
            }
            loadAiCombinationGraphCache();

        } catch (Exception e) {
            log.error("AI组卷二级缓存加载 失败", e);
        }
    }

    /**
     * 加载缓存
     */
    private void loadAiCombinationGraphCache() {
        long start = System.currentTimeMillis();

        for (String phaseSubject : phaseSubjects) {
            log.info("AI组卷二级图谱缓存开始，学段学科:{}", phaseSubject);
            List<String> ph = StrUtil.split(phaseSubject, "-");
            //学科学段过滤
            JSONObject props = new JSONObject().fluentPut("phase", ph.get(0)).fluentPut("subject", ph.get(1));
            GraphData graphData = localGraphService.lookup(new GraphLabelQuery().setGraphVersion(graphVersion).setTraceId(IdUtil.fastSimpleUUID()).setLabel(UNIT), props);
            //查询所有UNIT
            List<String> unitPoints = new ArrayList<>();
            unitPoints.addAll(graphData.getVertices().stream().map(GraphData.GraphVertex::getId).collect(Collectors.toList()));
            int j = 0;
            ConcurrentHashMap<String, GraphData> pressBookCache = new ConcurrentHashMap<>();
            for (String cata : unitPoints) {
                log.debug("学段学科:{},加载unitPoints缓存进度 : {}=====：{}/{}", phaseSubject, cata, j++, unitPoints.size());
                //截取
                List<String> split = StrUtil.split(cata, "_");
                if (CollectionUtils.isEmpty(split) || split.size() < 2) {
                    continue;
                }
                String press = split.get(0);
                String book = split.get(0) + "_" + split.get(1);

                //书本下各级目录的锚点、考点子图
                GraphData bookData = queryBook(book);
                BOOK_CACHE.put(book, bookData);

                //教材版本+学科+学段到书本信息子图
                GraphData pressBookData = queryPressBook(press);
                pressBookCache.put(press, pressBookData);

            }
            PHASE_SUBJECT_PRESS_BOOK_CACHE.put(phaseSubject, pressBookCache);

            if (!CollectionUtils.isEmpty(BOOK_CACHE)) {
                long startAnchorPoint = System.currentTimeMillis();
                List<GraphData> valueList = BOOK_CACHE.values().stream().collect(Collectors.toList());
                Set<String> anchorPointIds = valueList.stream().filter(gd -> gd.getVertices() != null).flatMap(gd -> gd.getVertices().stream()).filter(vertex -> ANCHOR_POINT.equals(vertex.getLabel())).map(GraphData.GraphVertex::getId).collect(Collectors.toSet());
                for (String anchorPointId : anchorPointIds) {
                    //锚点到题子图 缓存
                    setCache(ANCHOR_POINT, anchorPointId);
                }
                log.info("AnchorPoint二级图谱缓存结束,耗时：{}", System.currentTimeMillis() - startAnchorPoint);

                long startCheckPoint = System.currentTimeMillis();

                Set<String> checkPointIds = valueList.stream().filter(gd -> gd.getVertices() != null).flatMap(gd -> gd.getVertices().stream()).filter(vertex -> CHECK_POINT.equals(vertex.getLabel())).map(GraphData.GraphVertex::getId).collect(Collectors.toSet());
                for (String checkPointId : checkPointIds) {
                    //考点到题子图 缓存
                    setCache(CHECK_POINT, checkPointId);
                }
                log.info("CheckPoint二级图谱缓存结束,耗时：{}", System.currentTimeMillis() - startCheckPoint);

            }

        }
        //清理一级缓存
        try {
            clearOffHeapVertexCache();
            clearOffHeapEdgeCache();
        } catch (Exception e) {
            log.error("AI组卷清理图谱堆外缓存报错");
        }

        log.info("AI组卷二级图谱缓存结束,耗时：{}", System.currentTimeMillis() - start);
    }

    /**
     * 彻底清理嵌套的堆外内存缓存:点内存
     */
    private void clearOffHeapVertexCache() {
        Map<String, ByteBuffer> offHeapVertexMap = localFileGraphCache.getOffHeapVertexMap();
        if (offHeapVertexMap == null) {
            return;
        }
        offHeapVertexMap.values().forEach(this::releaseBuffer);
        offHeapVertexMap.clear(); // 清空外层Map
    }

    /**
     * 彻底清理嵌套的堆外内存缓存:边内存
     */
    private void clearOffHeapEdgeCache() {
        Map<String, Map<String, ByteBuffer>> offHeapEdgeMap = localFileGraphCache.getOffHeapEdgeMap();
        if (offHeapEdgeMap == null) {
            return;
        }

        // 遍历外层Map
        Iterator<Map.Entry<String, Map<String, ByteBuffer>>> outerIter = offHeapEdgeMap.entrySet().iterator();
        while (outerIter.hasNext()) {
            Map.Entry<String, Map<String, ByteBuffer>> entry = outerIter.next();
            Map<String, ByteBuffer> innerMap = entry.getValue();

            if (innerMap != null) {
                // 遍历内层Map
                Iterator<Map.Entry<String, ByteBuffer>> innerIter = innerMap.entrySet().iterator();
                while (innerIter.hasNext()) {
                    Map.Entry<String, ByteBuffer> innerEntry = innerIter.next();
                    ByteBuffer buffer = innerEntry.getValue();

                    // 释放堆外内存
                    releaseBuffer(buffer);
                    innerIter.remove(); // 移除内层Map条目
                }
                innerMap.clear(); // 清空内层Map
            }

            outerIter.remove(); // 移除外层Map条目
        }
        offHeapEdgeMap.clear(); // 清空外层Map
    }



    private void releaseBuffer(ByteBuffer buffer) {
        if (buffer != null && buffer.isDirect()) {
            try {
                ((sun.nio.ch.DirectBuffer) buffer).cleaner().clean();
            } catch (Exception e) {
                log.error("AI组卷清理一级缓存报错");
            }
        }
    }


    /**
     * 提供引擎查询接口
     *
     * @param queryParam
     * @return graphData
     */
    public static GraphData query(QueryParam queryParam) {
        GraphData graphData = new GraphData();
        graphData.setVertices(new ArrayList<>());
        graphData.setEdges(new ArrayList<>());
        if (PRESS.equals(queryParam.getSearchType())) {
            return queryPressBook(queryParam, graphData);
        } else if (BOOK.equals(queryParam.getSearchType())) {
            return queryBook(queryParam, graphData);
        } else if (ANCHOR_POINT.equals(queryParam.getSearchType())) {
            return queryAnchorPoint(queryParam, graphData);
        } else if (CHECK_POINT.equals(queryParam.getSearchType())) {
            return queryCheckPoint(queryParam, graphData);
        } else {

        }
        return graphData;
    }

    /**
     * 教材学科、学段到书本信息
     *
     * @param queryParam
     * @param graphData
     * @return
     */
    private static GraphData queryPressBook(QueryParam queryParam, GraphData graphData) {
        if (StringUtils.isNotEmpty(queryParam.getPhaseCode()) && StringUtils.isNotEmpty(queryParam.getSubjectCode())) {
            StringBuffer stringBuffer = new StringBuffer();
            stringBuffer.append(queryParam.getPhaseCode()).append("-").append(queryParam.getSubjectCode());
            for (String cata : queryParam.getRootVertexIdList()) {
                if (ObjectUtil.isNotEmpty(PHASE_SUBJECT_PRESS_BOOK_CACHE.get(stringBuffer.toString())) && ObjectUtil.isNotEmpty(PHASE_SUBJECT_PRESS_BOOK_CACHE.get(stringBuffer.toString()).get(cata))) {
                    graphData.getVertices().addAll(PHASE_SUBJECT_PRESS_BOOK_CACHE.get(stringBuffer.toString()).get(cata).getVertices());
                    graphData.getEdges().addAll(PHASE_SUBJECT_PRESS_BOOK_CACHE.get(stringBuffer.toString()).get(cata).getEdges());
                }
            }
        }
        return graphData;
    }

    /**
     * 书本下各级目录的锚点、考点子图
     *
     * @param queryParam
     * @param graphData
     * @return graphData
     */
    private static GraphData queryBook(QueryParam queryParam, GraphData graphData) {
        for (String cata : queryParam.getRootVertexIdList()) {
            if (ObjectUtil.isNotEmpty(BOOK_CACHE.get(cata))) {
                graphData.getVertices().addAll(BOOK_CACHE.get(cata).getVertices());
                graphData.getEdges().addAll(BOOK_CACHE.get(cata).getEdges());
            }
        }
        return graphData;
    }

    /**
     * 查询锚点到题
     *
     * @param queryParam
     * @param graphData
     * @return graphData
     */
    private static GraphData queryAnchorPoint(QueryParam queryParam, GraphData graphData) {
        for (String cata : queryParam.getRootVertexIdList()) {
            if (ObjectUtil.isNotEmpty(ANCHOR_POINT_TO_TOPIC_CACHE.get(cata))) {
                graphData.getVertices().addAll(ANCHOR_POINT_TO_TOPIC_CACHE.get(cata).getVertices());
                graphData.getEdges().addAll(ANCHOR_POINT_TO_TOPIC_CACHE.get(cata).getEdges());
            }
        }

        return graphData;
    }

    /**
     * 查询考点到题
     *
     * @param queryParam
     * @param graphData
     * @return queryParam
     */
    private static GraphData queryCheckPoint(QueryParam queryParam, GraphData graphData) {
        for (String cata : queryParam.getRootVertexIdList()) {
            if (ObjectUtil.isNotEmpty(CHECK_POINT_TO_TOPIC_CACHE.get(cata))) {
                graphData.getVertices().addAll(CHECK_POINT_TO_TOPIC_CACHE.get(cata).getVertices());
                graphData.getEdges().addAll(CHECK_POINT_TO_TOPIC_CACHE.get(cata).getEdges());
            }
        }
        return graphData;
    }

    /**
     * 教材版本+学科+学段到书本信息子图
     *
     * @param press
     * @return
     */
    private GraphData queryPressBook(String press) {

        SubGraphQuery subGraphQuery = new SubGraphQuery();
        subGraphQuery.setTraceId(UUID.randomUUID().toString());
        subGraphQuery.setGraphVersion(graphVersion);
        subGraphQuery.setRootVertexLabel(PRESS);
        subGraphQuery.setRootVertexIdList(Arrays.asList(press));
        List<SubGraphQuery.EdgeLabel> edgeLabels = new ArrayList<>();

        edgeLabels.add(SubGraphQuery.EdgeLabel.builder().source(PRESS).target(BOOK).build());
        subGraphQuery.setEdgeLabels(edgeLabels);

        GraphData graphData = localGraphService.querySubGraph(subGraphQuery);

        return graphData;
    }


    /**
     * 书本下各级目录的锚点、考点子图
     *
     * @param book
     * @return
     */
    private GraphData queryBook(String book) {

        SubGraphQuery subGraphQuery = new SubGraphQuery();
        subGraphQuery.setTraceId(UUID.randomUUID().toString());
        subGraphQuery.setGraphVersion(graphVersion);
        subGraphQuery.setRootVertexLabel(BOOK);
        subGraphQuery.setRootVertexIdList(Arrays.asList(book));
        List<SubGraphQuery.EdgeLabel> edgeLabels = new ArrayList<>();

        edgeLabels.add(SubGraphQuery.EdgeLabel.builder().source(BOOK).target(UNIT).build());
        edgeLabels.add(SubGraphQuery.EdgeLabel.builder().source(UNIT).target(COURSE).build());
        edgeLabels.add(SubGraphQuery.EdgeLabel.builder().source(UNIT).target("PERIOD").build());
        edgeLabels.add(SubGraphQuery.EdgeLabel.builder().source(COURSE).target("PERIOD").build());
        edgeLabels.add(SubGraphQuery.EdgeLabel.builder().source(COURSE).target("L2COURSE").build());
        edgeLabels.add(SubGraphQuery.EdgeLabel.builder().source(COURSE).target(CHECK_POINT).build());
        edgeLabels.add(SubGraphQuery.EdgeLabel.builder().source(COURSE).target(ANCHOR_POINT).build());
        edgeLabels.add(SubGraphQuery.EdgeLabel.builder().source(UNIT).target(CHECK_POINT).build());
        edgeLabels.add(SubGraphQuery.EdgeLabel.builder().source(UNIT).target(ANCHOR_POINT).build());
        edgeLabels.add(SubGraphQuery.EdgeLabel.builder().source("PERIOD").target(ANCHOR_POINT).build());
        edgeLabels.add(SubGraphQuery.EdgeLabel.builder().source("PERIOD").target(CHECK_POINT).build());
        edgeLabels.add(SubGraphQuery.EdgeLabel.builder().source("L2COURSE").target(ANCHOR_POINT).build());
        edgeLabels.add(SubGraphQuery.EdgeLabel.builder().source("L2COURSE").target(CHECK_POINT).build());
        edgeLabels.add(SubGraphQuery.EdgeLabel.builder().source(CHECK_POINT).target(ANCHOR_POINT).build());
        subGraphQuery.setEdgeLabels(edgeLabels);

        GraphData graphData = localGraphService.querySubGraph(subGraphQuery);

        return graphData;
    }


    /**
     * 加缓存
     *
     * @param type
     * @param id
     */
    private void setCache(String type, String id) {

        SubGraphQuery graphQuery = new SubGraphQuery();
        graphQuery.setTraceId(UUID.randomUUID().toString());
        graphQuery.setGraphVersion(graphVersion);
        graphQuery.setRootVertexLabel(type);
        graphQuery.setRootVertexIdList(Arrays.asList(id));
        //只查询边关系
        List<SubGraphQuery.EdgeLabel> edgeLabels = new ArrayList<>();
        edgeLabels.add(SubGraphQuery.EdgeLabel.builder().source(type).target(GraphVertexType.TOPIC).build());
        graphQuery.setEdgeLabels(edgeLabels);
        GraphData graphData = localGraphService.querySubGraph(graphQuery);

        if (ANCHOR_POINT.equals(type)) {
            //锚点到题子图
            ANCHOR_POINT_TO_TOPIC_CACHE.put(id, graphData);
        } else if (CHECK_POINT.equals(type)) {
            //考点到题子图
            CHECK_POINT_TO_TOPIC_CACHE.put(id, graphData);
        } else {

        }
    }


}
