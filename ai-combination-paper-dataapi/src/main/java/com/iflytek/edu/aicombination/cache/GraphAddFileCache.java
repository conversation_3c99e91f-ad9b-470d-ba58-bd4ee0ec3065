package com.iflytek.edu.aicombination.cache;

import com.alibaba.fastjson2.JSONObject;
import com.alibaba.fastjson2.TypeReference;
import com.iflytek.edu.aicombination.constant.GraphConstant;
import com.iflytek.edu.aicombination.dto.Attribute;
import com.iflytek.edu.aicombination.dto.MiddleSchoolMathProblemDTO;
import com.iflytek.edu.aicombination.dto.XkbProperty;
import com.iflytek.skylab.core.constant.GraphVertexType;
import com.iflytek.skylab.core.dataapi.data.GraphData;
import com.iflytek.skylab.core.dataapi.service.cache.LocalFileGraphCache;
import com.iflytek.skylab.core.dataapi.util.OffHeapObjUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.io.BufferedReader;
import java.io.File;
import java.io.IOException;
import java.io.InputStreamReader;
import java.nio.ByteBuffer;
import java.nio.charset.Charset;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.zip.ZipEntry;
import java.util.zip.ZipFile;

/**
 * 图谱新增文件缓存
 */
@Component
public class GraphAddFileCache {

    private static final Logger log = LoggerFactory.getLogger(GraphAddFileCache.class);

    @Resource
    private LocalFileGraphCache localFileGraphCache;

    /**
     * 新增缓存文件路径
     */
    @Value("${skylab.data.api.middle.math.path}")
    private String middleMathPath;

    /**
     * 新增初中数学题（错题本和阶段测名校板块）缓存标识
     * true-成功 false失败
     */
    private boolean updateCacheFlag = false;

    @PostConstruct
    public void initFileGraph() {
        try {
            //循环等待缓存完成
            while (!localFileGraphCache.isInitialized()) {
                Thread.sleep(5000);
            }
            //获取点/边缓存
            Map<String, Map<String, ByteBuffer>> offHeapEdgeMap = localFileGraphCache.getOffHeapEdgeMap();
            Map<String, ByteBuffer> offHeapVertexMap = localFileGraphCache.getOffHeapVertexMap();
            //读取要新增的zip文件
            File localGraphFile = new File(middleMathPath);
            if (null == localGraphFile || !localGraphFile.exists()) {
                throw new RuntimeException("新增数据文件不存在");
            }
            try (ZipFile localGraphZip = new ZipFile(localGraphFile)) {
                // 读取ZIP包中的文件列表
                Enumeration<? extends ZipEntry> entries = localGraphZip.entries();
                //遍历文件
                while (entries.hasMoreElements()) {
                    ZipEntry entry = entries.nextElement();
                    //读文件
                    try (BufferedReader reader = new BufferedReader(new InputStreamReader(localGraphZip.getInputStream(entry), Charset.forName(GraphConstant.FILE_FORMAT_UTF8)))) {
                        processZipFile(reader, offHeapEdgeMap, offHeapVertexMap);
                    }
                }
            }
            //将修改后的数据set回缓存中
            localFileGraphCache.setOffHeapEdgeMap(offHeapEdgeMap);
            localFileGraphCache.setOffHeapVertexMap(offHeapVertexMap);
            //新增题成功，修改标识
            updateCacheFlag = true;
        } catch (Exception e) {
            log.error("新增初中数学题（错题本和阶段测名校板块）失败", e);
            throw new RuntimeException("数据文件加载失败", e);
        }
    }

    /**
     *  解析文件
     */
    private void processZipFile(BufferedReader reader, Map<String, Map<String, ByteBuffer>> offHeapEdgeMap, Map<String, ByteBuffer> offHeapVertexMap) throws IOException, ClassNotFoundException {
        //已反挂的新课标属性的锚点
        Set<String> anchorIdlist = new HashSet<>();
        //需要新增的题
        Set<String> topicIdList = new HashSet<>();
        //需要修改题上新课标属性
        Set<String> updateTopicIdList = new HashSet<>();
        String line;
        //新增边 锚点->题
        int addEdgeNum = 0;
        //因 题已经在图谱中/锚点不存在图谱 未添加的题
        int notAddTopic = 0;
        //已经在图谱中存在的题
        int existTopic = 0;
        //不在边文件EDGE_EXAM_POINT_TOPIC数据
        AtomicInteger notExistExamTopicNum = new AtomicInteger(0);
        //不在边文件EDGE_ANCHOR_POINT_EXAM_POINT数据
        AtomicInteger notExistAnchorExamNum = new AtomicInteger(0);
        while ((line = reader.readLine()) != null) {
            try {
                MiddleSchoolMathProblemDTO problems = JSONObject.parseObject(line, MiddleSchoolMathProblemDTO.class);
                String topicId = problems.getTopic_id();
                String anchorId = problems.getAnchor_id();
                ByteBuffer topicBuffer = offHeapVertexMap.get(topicId);
                ByteBuffer anchorIdBuffer = offHeapVertexMap.get(anchorId);
                //只有题不存在图谱，且锚点存在图谱，才补充题 （新增题不用没有新课标属性，无需添加和反挂新课标属性到锚点）
                if (null == topicBuffer && null != anchorIdBuffer) {
                    //新增点-题
                    addTopic(offHeapVertexMap, problems);
                    topicIdList.add(topicId);

                    //锚点->题
                    addEdge(offHeapEdgeMap, problems);
                    //本次添加的题，且对应锚点在图谱中，添加边
                } else if (topicIdList.contains(topicId) && null != anchorIdBuffer) {
                    //锚点->题
                    addEdge(offHeapEdgeMap, problems);
                    addEdgeNum++;
                    //存在图谱的题，锚点也存在，需要将题上新课标属性反挂锚点上，题上也需要新课标属性。新课标属性来源 锚点----考法点----试题  考法与试题边取 EXAM_POINT_TOPIC.xgkLabe 属性
                } else if (null != topicBuffer && null != anchorIdBuffer) {
                    existTopic++;
                    //获取新课标属性
                    List<Attribute> xkbProperties = getXKBProperties(offHeapEdgeMap, anchorId, topicId, notExistExamTopicNum, notExistAnchorExamNum);
                    //存在新课标属性（排除数学情境）
                    if (CollectionUtils.isEmpty(xkbProperties)) {
                        continue;
                    }
                    //反挂到锚点上
                    updateAnchorInfo(offHeapVertexMap, anchorIdBuffer, xkbProperties, anchorIdlist);

                    //修改题新课标属性
                    updateTopic(offHeapVertexMap, topicBuffer, xkbProperties, updateTopicIdList);
                }else {
                    notAddTopic++;
                    log.info("题={}，锚点={} 锚点不在图谱中, 不添加题", topicId, anchorId);
                }
            }catch (Exception e) {
                log.error("数据处理失败 line={}", line);
                throw new RuntimeException(e);
            }

        }
        log.info("添加题点数量={}，边数量={}，图谱中重复题数量={}，未处理的题数量={}, 反挂新课标属性的锚点数量={}, 原初数题挂新课标属性的数量={}, 不在边文件EDGE_EXAM_POINT_TOPIC数据={}, 不在边文件EDGE_ANCHOR_POINT_EXAM_POINT数据={}",
                topicIdList.size(), addEdgeNum, existTopic, notAddTopic, anchorIdlist.size(), updateTopicIdList.size(), notExistExamTopicNum, notExistAnchorExamNum);
    }

    /**
     * 获取 考法点->题 上的新课标属性
     * @param offHeapEdgeMap 边缓存
     * @param anchorId 锚点
     * @param topicId 题
     */
    private List<Attribute> getXKBProperties(Map<String, Map<String, ByteBuffer>> offHeapEdgeMap, String anchorId, String topicId, AtomicInteger notExistExamTopicNum, AtomicInteger notExistAnchorExamNum) throws IOException, ClassNotFoundException {
        //读取边 锚点->考法点
        Map<String, ByteBuffer> anchorPointExamPointEdgeMap = offHeapEdgeMap.get(GraphConstant.EDGE_ANCHOR_POINT_EXAM_POINT);
        //边文件不为空，且锚点边不为空
        if (!CollectionUtils.isEmpty(anchorPointExamPointEdgeMap) && null != anchorPointExamPointEdgeMap.get(anchorId)) {
            ByteBuffer anchorPointExamPointEdgeBuffer = anchorPointExamPointEdgeMap.get(anchorId);
            List<GraphData.GraphEdge> anchorPointTopicEdgeList = OffHeapObjUtils.readObjectFromOffHeap(anchorPointExamPointEdgeBuffer);
            for (GraphData.GraphEdge anchorPointTopicEdge : anchorPointTopicEdgeList) {
                //考法点
                String examPoint = anchorPointTopicEdge.getTarget();
                //读取边 考法点->题
                Map<String, ByteBuffer> examPointTopicEdgeMap = offHeapEdgeMap.get(GraphConstant.EDGE_EXAM_POINT_TOPIC);
                //边文件不为空，且考法点边不为空
                if (null != examPointTopicEdgeMap && null != examPointTopicEdgeMap.get(examPoint)) {
                    ByteBuffer examPointTopicEdgeBuffer = examPointTopicEdgeMap.get(examPoint);
                    List<GraphData.GraphEdge> examPointTopicEdgeList = OffHeapObjUtils.readObjectFromOffHeap(examPointTopicEdgeBuffer);
                    //获取 考法点
                    for (GraphData.GraphEdge examPointTopicEdge : examPointTopicEdgeList) {
                        String topicIdTarget = examPointTopicEdge.getTarget();
                        //拿到 考法点->题 上对应的新课标属性并返回
                        if (topicIdTarget.equals(topicId)) {
                            JSONObject properties = examPointTopicEdge.getProperties();
                            //处理属性 这里排除 新情境 中的 数学情境
                            return excludeAttribute(properties);
                        }
                    }
                } else {
                    notExistExamTopicNum.incrementAndGet();
                }
            }
        } else {
            notExistAnchorExamNum.incrementAndGet();
        }
        return null;
    }

    /**
     * 排除 新情境 中的 数学情境
     * @param properties 属性
     */
    private List<Attribute> excludeAttribute(JSONObject properties) {
        if (null == properties || StringUtils.isEmpty(properties.getString(GraphConstant.XKB_LABEL))) {
            return null;
        }
        String xgkLabelStr = properties.getString(GraphConstant.XKB_LABEL);
        List<XkbProperty> xkbProperties = JSONObject.parseObject(xgkLabelStr, new TypeReference<List<XkbProperty>>() {});
        List<Attribute> list = new ArrayList<>();
        for (XkbProperty xgkLabel : xkbProperties) {
            String code = xgkLabel.getCode();
            String name = xgkLabel.getName();
            List<Attribute> children = xgkLabel.getChildren();
            for (Attribute child : children) {
                if (!GraphConstant.XKB_LABEL_XQJ_CODE.equals(code) && !GraphConstant.XKB_LABEL_XQJ_CODE_0002.equals(child.getCode())) {
                    Attribute attribute = new Attribute();
                    String labelCode = code + GraphConstant.ATTRIBUTE_SEPARATOR + child.getCode();
                    String labelName = name + GraphConstant.ATTRIBUTE_SEPARATOR + child.getName();
                    attribute.setCode(labelCode);
                    attribute.setName(labelName);
                    list.add(attribute);
                }
            }
        }
        return list;
    }

    /**
     * 修改题上的新课标属性
     * @param offHeapVertexMap 点Map
     * @param topicBuffer   题点buffer
     * @param xkbProperties  新课标属性
     * @param updateTopicIdList 已经修改属性的题List
     */
    private void updateTopic(Map<String, ByteBuffer> offHeapVertexMap, ByteBuffer topicBuffer, List<Attribute> xkbProperties, Set<String> updateTopicIdList) throws IOException, ClassNotFoundException {
        if (null == topicBuffer) {
            return;
        }
        GraphData.GraphVertex topicInfo = OffHeapObjUtils.readObjectFromOffHeap(topicBuffer);
        String topicId = topicInfo.getId();
        //存在新课标属性，且题没有修改
        if (!CollectionUtils.isEmpty(xkbProperties) && !updateTopicIdList.contains(topicId)) {
            updateTopicIdList.add(topicId);
            topicInfo.getProperties().fluentPut(GraphConstant.TOPIC_XKB_CORE_ATTAINMENT, JSONObject.toJSONString(xkbProperties));
            offHeapVertexMap.put(topicId, OffHeapObjUtils.writeObjectToOffHeap(topicInfo));
            //清除原本堆外内存
            freeOffHeapMemory(topicBuffer);
        }
    }

    /**
     * 新课标反挂到锚点上(初中锚点新课标字段为空，直接覆盖)
     * @param anchorIdBuffer    锚点buffer
     * @param xkbProperties      新课标属性
     * @param anchorIdList      已经修改过新课标属性的锚点
     */
    private void updateAnchorInfo(Map<String, ByteBuffer> offHeapVertexMap, ByteBuffer anchorIdBuffer, List<Attribute> xkbProperties, Set<String> anchorIdList) throws IOException, ClassNotFoundException {
        if (null == anchorIdBuffer) {
            return;
        }
        GraphData.GraphVertex anchorInfo = OffHeapObjUtils.readObjectFromOffHeap(anchorIdBuffer);
        String anchorId = anchorInfo.getId();
        //存在新课标属性，且未反挂锚点
        if (!CollectionUtils.isEmpty(xkbProperties) && !anchorIdList.contains(anchorId)) {
            anchorIdList.add(anchorId);
            anchorInfo.getProperties().fluentPut(GraphConstant.ANCHOR_CLASS_STANDARD_FEATURES, JSONObject.toJSONString(xkbProperties));
            offHeapVertexMap.put(anchorId, OffHeapObjUtils.writeObjectToOffHeap(anchorInfo));
            //清除原本堆外内存
            freeOffHeapMemory(anchorIdBuffer);
        }
    }

    /**
     * 新增点-题
     * @param offHeapVertexMap  点Map
     * @param problems  新增题
     */
    private void addTopic(Map<String, ByteBuffer> offHeapVertexMap, MiddleSchoolMathProblemDTO problems) throws IOException {
        String topicId = problems.getTopic_id();
        JSONObject properties = new JSONObject();
        properties.fluentPut(GraphConstant.TOPIC_DIFFICULTY, problems.getDifficulty_code());
        properties.fluentPut(GraphConstant.TOPIC_TYPE, problems.getQuestion_small_type_code());
        GraphData.GraphVertex topicVertex = GraphData.GraphVertex.builder().id(topicId).label(GraphVertexType.TOPIC).properties(properties).build();
        offHeapVertexMap.put(topicId, OffHeapObjUtils.writeObjectToOffHeap(topicVertex));
    }

    /**
     * 添加 锚点->题
     * @param offHeapEdgeMap 边map
     * @param problems  新增题
     */
    private void addEdge(Map<String, Map<String, ByteBuffer>> offHeapEdgeMap, MiddleSchoolMathProblemDTO problems) throws IOException, ClassNotFoundException {
        String topicId = problems.getTopic_id();
        String anchorId = problems.getAnchor_id();
        //生成新的边数据
        GraphData.GraphEdge anchorPointTopicEdge = GraphData.GraphEdge.builder().source(anchorId).target(topicId).build();
        //读取边 锚点->题  数据结构 Map<文件名, Map<String,ByteBuffer>>  再读取是 LinkedList<GraphData.GraphEdge>
        Map<String, ByteBuffer> map = offHeapEdgeMap.get(GraphConstant.EDGE_ANCHOR_POINT_TOPIC);
        if (!CollectionUtils.isEmpty(map)) {
            ByteBuffer anchorPointTopicEdgeBuffer = map.get(anchorId);
            if (null != anchorPointTopicEdgeBuffer) {
                List<GraphData.GraphEdge> anchorPointTopicEdgeList = OffHeapObjUtils.readObjectFromOffHeap(anchorPointTopicEdgeBuffer);
//                log.info("边旧属性={}", anchorPointTopicEdgeList.size());
                if (CollectionUtils.isEmpty(anchorPointTopicEdgeList)) {
                    anchorPointTopicEdgeList = new LinkedList<>();
                }
                anchorPointTopicEdgeList.add(anchorPointTopicEdge);
                //添加到map里面
                map.put(anchorId, OffHeapObjUtils.writeObjectToOffHeap(anchorPointTopicEdgeList));
                //清除原本堆外内存
                freeOffHeapMemory(anchorPointTopicEdgeBuffer);
            } else {    //这里可能是锚点在图谱中，但是锚点下面没有边
                List<GraphData.GraphEdge> anchorPointTopicEdgeList = new LinkedList<>();
                anchorPointTopicEdgeList.add(anchorPointTopicEdge);
                //添加到map里面
                map.put(anchorId, OffHeapObjUtils.writeObjectToOffHeap(anchorPointTopicEdgeList));
                log.info("新增边锚点->题，锚点={},题={}", anchorId, topicId);
            }
        } else {
            log.error("边文件EDGE_ANCHOR_POINT_TOPIC数据不存在");
        }
    }

    /**
     * 获取新增文件缓存状态
     */
    public boolean isUpdateCacheFlag() {
        return updateCacheFlag;
    }

    public void freeOffHeapMemory(ByteBuffer buffer) {
        if (buffer != null && buffer.isDirect()) {
            ((sun.nio.ch.DirectBuffer)buffer).cleaner().clean();
        }
    }


}
