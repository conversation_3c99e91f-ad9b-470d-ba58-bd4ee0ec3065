package com.iflytek.skylab.core.dataapi.test;

import com.alibaba.fastjson2.JSON;
import com.iflytek.edu.aicombination.cache.AiCombinationGraphCache;
import com.iflytek.edu.aicombination.dto.QueryParam;
import com.iflytek.skylab.core.dataapi.data.GraphData;
import com.iflytek.skylab.core.dataapi.data.SubGraphQuery;
import com.iflytek.skylab.core.dataapi.service.GraphService;
import com.iflytek.skylab.core.dataapi.service.cache.LocalFileGraphCache;
import lombok.extern.slf4j.Slf4j;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;
import skynet.boot.annotation.EnableSkynetLogging;
import skynet.boot.logging.LoggingCost;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.UUID;

import static com.iflytek.skylab.core.constant.GraphVertexType.*;


@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT, classes = DataHubTestBoot.class)
public class AiGraphServiceImplTest {


    @Autowired
    @Qualifier("localGraphService")
    private GraphService localGraphService;

    @Autowired
    LocalFileGraphCache diagGraphCache;

    @Autowired
    AiCombinationGraphCache aiCombinationGraphCache;

    @Test
    public void querySubGraphNeedProps() {
//        GraphData graphData1 = AiCombinationGraphCache.queryCheckPoint(Arrays.asList("ffeaa8ae-0e93-4628-8670-901eae305bb3"));
        long a = 0;
        for (int i = 0; i < 1; i++) {
            long currentTimeMillis = System.currentTimeMillis();
            SubGraphQuery subGraphQuery = new SubGraphQuery();
            subGraphQuery.setTraceId(UUID.randomUUID().toString());
            subGraphQuery.setGraphVersion("20250415_001");
            subGraphQuery.setRootVertexLabel("ANCHOR_POINT");
            subGraphQuery.setRootVertexIdList(Arrays.asList("ffeaa8ae-0e93-4628-8670-901eae305bb3"));
            List<SubGraphQuery.EdgeLabel> edgeLabels = new ArrayList<>();
//            edgeLabels.add(SubGraphQuery.EdgeLabel.builder().source("UNIT").target("ANCHOR_POINT").build());
//            edgeLabels.add(SubGraphQuery.EdgeLabel.builder().source("UNIT").target("COURSE").build());
//            edgeLabels.add(SubGraphQuery.EdgeLabel.builder().source("UNIT").target("PERIOD").build());
//            edgeLabels.add(SubGraphQuery.EdgeLabel.builder().source("UNIT").target("LEARN_PATH").build());
//            edgeLabels.add(SubGraphQuery.EdgeLabel.builder().source("COURSE").target("PERIOD").build());
//            edgeLabels.add(SubGraphQuery.EdgeLabel.builder().source("COURSE").target("ANCHOR_POINT").build());
//            edgeLabels.add(SubGraphQuery.EdgeLabel.builder().source("COURSE").target("LEARN_PATH").build());
//            edgeLabels.add(SubGraphQuery.EdgeLabel.builder().source("COURSE").target("L2COURSE").build());
//            edgeLabels.add(SubGraphQuery.EdgeLabel.builder().source("PERIOD").target("LEARN_PATH").build());
//            edgeLabels.add(SubGraphQuery.EdgeLabel.builder().source("PERIOD").target("ANCHOR_POINT").build());
//            edgeLabels.add(SubGraphQuery.EdgeLabel.builder().source("L2COURSE").target("L3COURSE").build());
//            edgeLabels.add(SubGraphQuery.EdgeLabel.builder().source("L2COURSE").target("LEARN_PATH").build());
//            edgeLabels.add(SubGraphQuery.EdgeLabel.builder().source("L2COURSE").target("ANCHOR_POINT").build());
//            edgeLabels.add(SubGraphQuery.EdgeLabel.builder().source("L3COURSE").target("ANCHOR_POINT").build());
//            edgeLabels.add(SubGraphQuery.EdgeLabel.builder().source("L3COURSE").target("LEARN_PATH").build());
//            edgeLabels.add(SubGraphQuery.EdgeLabel.builder().source("LEARN_PATH").target("ANCHOR_POINT").build());
//            edgeLabels.add(SubGraphQuery.EdgeLabel.builder().source("ANCHOR_POINT").target("ANCHOR_POINT").build());
            edgeLabels.add(SubGraphQuery.EdgeLabel.builder().source("ANCHOR_POINT").target("TOPIC").build());
            subGraphQuery.setEdgeLabels(edgeLabels);
//            Map<String, List<String>> nodeProps = new HashMap<>();
//            nodeProps.put("ANCHOR_POINT", Arrays.asList("anchorPointType", "difficulty", "evaluatePoint", "evaluations", "examPoint", "phaseCode", "realLastLevelRelationCatas", "subjectCode", "tracePoints"));
//            nodeProps.put("LEARN_PATH", Arrays.asList("pathType"));
//            subGraphQuery.setNodeProps(null);


//            subGraphQuery.setTraceId(UUID.randomUUID().toString());
//            subGraphQuery.setGraphVersion("v2");
//            subGraphQuery.setRootVertexLabel("COURSE");
//            subGraphQuery.setRootVertexIdList(Arrays.asList("01_07020101-001_02_001"));
//            List<SubGraphQuery.EdgeLabel> edgeLabels = new ArrayList<>();
//            edgeLabels.add(SubGraphQuery.EdgeLabel.builder().source("COURSE").target("ANCHOR_POINT").build());
//            subGraphQuery.setEdgeLabels(edgeLabels);
            GraphData graphData = localGraphService.querySubGraph(subGraphQuery);
//            System.out.println(graphData);
//            Assert.assertTrue(graphData != null);
//            Assert.assertTrue(!graphData.getEdges().isEmpty());
//            Assert.assertTrue(!graphData.getVertices().isEmpty());
            long end = System.currentTimeMillis() - currentTimeMillis;
            System.err.println("耗时：" + end + " 图谱长度" + JSON.toJSONString(graphData).length());

        }
    }

    @Test
    public void queryAnchorPoint() {
        long currentTimeMillis = System.currentTimeMillis();
        QueryParam queryParam = new QueryParam();
        queryParam.setPhaseCode("04");
        queryParam.setSubjectCode("02");

        queryParam.setSearchType(ANCHOR_POINT);
        queryParam.setRootVertexIdList(Arrays.asList("12fe19e7-e84e-4b32-be2d-4e4091fc331f"));
        GraphData graphData = AiCombinationGraphCache.query(queryParam);

        long end = System.currentTimeMillis() - currentTimeMillis;

        System.out.println("耗时：" + end + " 图谱长度" + JSON.toJSONString(graphData).length());

    }
//
//    @Test
//    public void queryBook() {
//        long currentTimeMillis = System.currentTimeMillis();
//
//        GraphData graphData = AiCombinationGraphCache.queryBook(Arrays.asList("01_01020101-001", "01_01020201-003", "01_02020101-003", "01_02020201-003", "01_03020101-003", "01_03020201-002", "01_04020101-003", "01_04020201-002", "01_05020101-003", "01_05020201-002", "01_06020101-003", "01_06020201-002"));
//
//        long end = System.currentTimeMillis() - currentTimeMillis;
//
//        System.out.println("耗时：" + end + " 图谱长度" + JSON.toJSONString(graphData));
//
//    }

    @Test
    public void queryCheckPoint() {
        long currentTimeMillis = System.currentTimeMillis();
        QueryParam queryParam = new QueryParam();
        queryParam.setSearchType(CHECK_POINT);
        queryParam.setRootVertexIdList(Arrays.asList("b7fc1e16-cadf-41a0-a50a-39b023b51b28"));
        GraphData graphData = AiCombinationGraphCache.query(queryParam);

        long end = System.currentTimeMillis() - currentTimeMillis;

        System.out.println("耗时：" + end + " 图谱长度" + JSON.toJSONString(graphData));

    }

    @Test
    public void query() {
        long currentTimeMillis = System.currentTimeMillis();
        QueryParam queryParam = new QueryParam();
        queryParam.setPhaseCode("04");
        queryParam.setSubjectCode("02");

        queryParam.setSearchType(PRESS);
        queryParam.setRootVertexIdList(Arrays.asList("22"));
        GraphData graphData = AiCombinationGraphCache.query(queryParam);

        long end = System.currentTimeMillis() - currentTimeMillis;

        System.out.println("耗时：" + end + " 图谱长度" + JSON.toJSONString(graphData));

    }

    @Test
    public void queryBook() {
        long currentTimeMillis = System.currentTimeMillis();
        QueryParam queryParam = new QueryParam();
        queryParam.setPhaseCode("04");
        queryParam.setSubjectCode("02");

        queryParam.setSearchType(BOOK);
        queryParam.setRootVertexIdList(Arrays.asList("01_01020101-001", "01_01020201-003", "01_02020101-003", "01_02020201-003", "01_03020101-003", "01_03020201-002", "01_04020101-003", "01_04020201-002", "01_05020101-003", "01_05020201-002", "01_06020101-003", "01_06020201-002"));
        GraphData graphData = AiCombinationGraphCache.query(queryParam);

        long end = System.currentTimeMillis() - currentTimeMillis;
        System.out.println("耗时：" + end + " 图谱长度" + JSON.toJSONString(graphData));

    }
}
