#-----------------------------------------------------------------
skyline.data.api.sdk.keep-alive-duration-second=5
skyline.data.api.sdk.max-idle-conn=5
skyline.data.api.sdk.timeout-ms=5000
skyline.data.api.sdk.timeoutMs=5000
skyline.data.api.sdk.maxIdleConn=10
skyline.data.api.sdk.queryLabelDataApiId=api-me0vg2e4
skyline.data.api.sdk.queryApiDataApiId=api-2rl8e4vh
#-----------------------------------------------------------------

#-----------------------------------------------------------------
skyline.spring.cloud.zookeeper.discovery.enabled=false
skyline.spring.cloud.zookeeper.discovery.register=false
skyline.spring.cloud.zookeeper.enabled=false
skynet.action-point=${spring.application.name}@skyline
#-----------------------------------------------------------------
IP=***********
server.port=88888
#-----------------------------------------------------------------
spring.application.name=skylab-dataapi
spring.cloud.zookeeper.connect-string=${IP}:2181
spring.cloud.zookeeper.discovery.root=/skynet/discovery/skylab
spring.cloud.zookeeper.discovery.enabled=true
#-----------------------------------------------------------------
#mongodb

logging.level.com.iflytek.skylab=INFO
#-----------------------------------------------------------------
skyline.data.api.sdk.app-key=app-69v3u6vj
skyline.data.api.sdk.app-secret=09bb950d5315a56305b92c558be03e4b8fcd1dcd
skyline.data.api.sdk.url=http://**************:30890/api/v1/execute
skyline.data.api.sdk.keepAliveDurationSecond=5
#-----------------------------------------------------------------
skylab.zion.dict-table-name=dim_xxj_dic_model
skylab.zion.dict-family=u
skylab.zion.dict-qualifier=dicModel
skylab.zion.dict-default-row-key=xxj.feature.dic.model#v2022-03
skylab.zion.dict-refresh-period-seconds=3600
skylab.zion.dict-data-api-item.dataApiId=api-vimqibeu
skylab.zion.dict-data-api-item.version=1
skylab.zion.cache-max-size=1000000
skylab.zion.feature-data-api-item.dataApiId=api-x4znzm0l
skylab.zion.feature-data-api-item.version=3
skylab.data.api.study-log.featureVersion=1
skylab.data.api.study-log.graphVersion=v2022-03
#-----------------------------------------------------------------
management.metrics.export.prometheus.enabled=true
skylab.data.api.graph.hosts=***********:9669
skylab.data.api.graph.username=root
#skylab.data.api.graph.password=root
skylab.data.api.graph.nodeProps.ANCHOR_POINT=anchorPointType,difficulty,examPoint,phaseCode,realLastLevelRelationCatas,subjectCode,falliblePoint,name,classStandardFeatures,difficultPoint,importantPoint
skylab.data.api.graph.nodeProps.CHECK_POINT=checkPointName
skylab.data.api.graph.nodeProps.LEARN_PATH=
skylab.data.api.graph.nodeProps.BOOK=name,phase,subject
skylab.data.api.graph.nodeProps.UNIT=name
skylab.data.api.graph.nodeProps.COURSE=name
skylab.data.api.graph.nodeProps.PERIOD=name
skylab.data.api.graph.nodeProps.L2COURSE=name
skylab.data.api.graph.nodeProps.L3COURSE=name

skylab.data.api.graph.nodeProps.REVIEW_POINT=examFrequency,difficulty
skylab.data.api.graph.nodeProps.TOPIC=topic_difficulty,topic_type
skylab.data.api.graph.cacheFiles=TAG_PRESS.info,TAG_BOOK.info,TAG_UNIT.info,TAG_COURSE.info,TAG_PERIOD.info,TAG_L2COURSE.info,TAG_CHECK_POINT.info,TAG_ANCHOR_POINT.info,TAG_TOPIC.info,EDGE_PRESS_BOOK.info,EDGE_BOOK_UNIT.info,EDGE_UNIT_COURSE.info,EDGE_UNIT_PERIOD.info,EDGE_COURSE_PERIOD.info,EDGE_COURSE_L2COURSE.info,EDGE_COURSE_CHECK_POINT.info,EDGE_COURSE_ANCHOR_POINT.info,EDGE_UNIT_CHECK_POINT.info,EDGE_UNIT_ANCHOR_POINT.info,EDGE_PERIOD_ANCHOR_POINT.info,EDGE_PERIOD_CHECK_POINT.info,EDGE_L2COURSE_ANCHOR_POINT.info,EDGE_L2COURSE_CHECK_POINT.info,EDGE_CHECK_POINT_ANCHOR_POINT.info,EDGE_ANCHOR_POINT_TOPIC.info,EDGE_EXAM_POINT_TOPIC.info,EDGE_ANCHOR_POINT_EXAM_POINT.info

ai.combination.graph-version=20250613_001
skylab.data.api.graph.localCacheEnabled=true
skylab.data.api.graph.cacheVersion=20250613_001
#skylab.data.api.graph.path=${SKYNET_PLUGIN_HOME}/nebulaData_20250219_001.zip
skylab.data.api.graph.path=D://work/tjpt/nebulaData_20250613_001.zip

zion.thread-core-pool-size=64
zion.thread-max-pool-size=1000
zion.query-timeout=2000
zion.es-dict-index-name=index-xxj-jzx-offline-feature-dict
zion.dict-qualifier=dicModel
zion.dict-default-row-key=xxj.feature.dic.model#v2022-03
zion.es-host=***********:9200,***********:9200,***********:9200
zion.es-user-name=elastic
#zion.es-password=bx90ZOw1IZbx8fWCIo64
zion.query-data-base=es
zion.dict-refresh-period-seconds=10
zion.cache-ttl=PT3M

skylab.data.api.middle.math.path=D://work/tjpt/newGraphData.zip

ai.combination.phase.subject=03-02,04-02