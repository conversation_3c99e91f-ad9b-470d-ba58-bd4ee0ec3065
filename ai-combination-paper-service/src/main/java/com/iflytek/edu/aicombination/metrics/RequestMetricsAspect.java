package com.iflytek.edu.aicombination.metrics;

import com.google.common.base.Stopwatch;

import com.iflytek.edu.aicombination.annotation.ServiceRequestMetrics;
import com.iflytek.edu.aicombination.base.BaseResponse;
import io.micrometer.core.instrument.Counter;
import io.micrometer.core.instrument.Gauge;
import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.Tags;
import io.micrometer.core.instrument.Timer;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import skynet.boot.AppContext;

import java.lang.reflect.Method;
import java.time.Duration;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * 接口监控切面
 * 用于收集接口的请求量、QPS、响应时间等指标
 * <AUTHOR>
 */
@Slf4j
@Aspect
@Component
public class RequestMetricsAspect {

    @Value("${spring.application.name}")
    private String application;

    private static final String DEFAULT_TAG_VALUE = "unknown";
    private static final String METRIC_PREFIX = "recommend_";
    
    private final String ip;
    private final MeterRegistry meterRegistry;
    private final ConcurrentHashMap<String, AtomicInteger> concurrentRequests = new ConcurrentHashMap<>();

    /**
     * 添加 Meter 缓存
     */
    private final ConcurrentHashMap<String, Counter> counterCache = new ConcurrentHashMap<>();
    private final ConcurrentHashMap<String, Timer> timerCache = new ConcurrentHashMap<>();
    private final ConcurrentHashMap<String, Gauge> gaugeCache = new ConcurrentHashMap<>();

    /**
     * 添加 QPS 相关的缓存
     */
    private final ConcurrentHashMap<String, Timer> qpsTimerCache = new ConcurrentHashMap<>();
    private final ConcurrentHashMap<String, Long> lastQpsTimeCache = new ConcurrentHashMap<>();

    /**
     * QPS 更新间隔，单位毫秒
     */
    private final long QPS_UPDATE_INTERVAL = 1000L;


    public RequestMetricsAspect(MeterRegistry meterRegistry, AppContext appContext) {
        this.meterRegistry = meterRegistry;
        this.ip = StringUtils.isBlank(appContext.getIpEndpoint()) ? DEFAULT_TAG_VALUE : appContext.getIpEndpoint();
    }

    @Around("@annotation(serviceRequestMetrics)")
    public Object around(ProceedingJoinPoint joinPoint, ServiceRequestMetrics serviceRequestMetrics) throws Throwable {
        String methodName = getMethodName(joinPoint);
        String desc = serviceRequestMetrics.desc();
        String type = serviceRequestMetrics.type();

        // 记录并发数
        AtomicInteger concurrentCount = concurrentRequests.computeIfAbsent(methodName, k -> new AtomicInteger(0));
        concurrentCount.incrementAndGet();
        
        // 记录开始时间
        Stopwatch stopWatch = Stopwatch.createStarted();
        Object result = null;
        // 默认成功码为0
        String errorCode = "0";
        
        try {
            result = joinPoint.proceed();
            // 解析响应码
            errorCode = parseErrorCode(result);
            return result;
        } catch (Throwable t) {
            // 系统异常使用unknown
            errorCode = DEFAULT_TAG_VALUE;
            throw t;
        } finally {
            // 减少并发计数
            concurrentCount.decrementAndGet();
            
            // 计算耗时
            Duration duration = stopWatch.stop().elapsed();

            // 构建标签键
            String tagKey = buildTagKey(desc, methodName, errorCode, type);

            // 记录总请求数
            Counter totalRequests = getOrCreateCounter(tagKey);
            totalRequests.increment();

            // 记录响应时间
            Timer responseTime = getOrCreateTimer(tagKey);
            responseTime.record(duration);

            // 记录并发数
            getOrCreateGauge(tagKey, concurrentCount);

            // 记录 QPS
            recordQPS(tagKey);

            if (log.isDebugEnabled()) {
                log.debug("Request metrics - service:{}, method:{}, errorCode:{}, duration:{}ms", 
                    desc, methodName, errorCode, duration.toMillis());
            }
        }
    }

    /**
     * 记录 QPS（使用 Timer 方式）
     */
    private void recordQPS(String tagKey) {
        long currentTime = System.currentTimeMillis();
        Long lastUpdateTime = lastQpsTimeCache.get(tagKey);

        // 每秒更新一次 QPS
        if (lastUpdateTime == null || currentTime - lastUpdateTime >= QPS_UPDATE_INTERVAL) {
            Timer qpsTimer = getOrCreateQPSTimer(tagKey);
            // 记录 1 毫秒，表示一次请求
            qpsTimer.record(Duration.ofMillis(1));
            lastQpsTimeCache.put(tagKey, currentTime);
        }
    }

    /**
     * 获取或创建 QPS Timer
     */
    private Timer getOrCreateQPSTimer(String tagKey) {
        return qpsTimerCache.computeIfAbsent(tagKey, key -> {
            String[] parts = key.split("\\|");
            Tags tags = Tags.of(
                    "application", parts[0],
                    "ip", parts[1],
                    "desc", parts[2],
                    "method", parts[3],
                    "error_code", parts[4],
                    "type", parts[5]
            );
            return Timer.builder(METRIC_PREFIX + "qps")
                    .tags(tags)
                    .register(meterRegistry);
        });
    }

    /**
     *  构建标签键
     */
    private String buildTagKey(String desc, String methodName, String errorCode, String type) {
        return String.join("|", application, ip, desc, methodName, errorCode, type);
    }

    /**
     * 获取或创建 Counter
     * @param tagKey
     * @return
     */
    private Counter getOrCreateCounter(String tagKey) {
        return counterCache.computeIfAbsent(tagKey, key -> {
            String[] parts = key.split("\\|");
            Tags tags = Tags.of(
                    "application", parts[0],
                    "ip", parts[1],
                    "desc", parts[2],
                    "method", parts[3],
                    "error_code", parts[4],
                    "type", parts[5]
            );
            return Counter.builder(METRIC_PREFIX + "requests_total")
                    .tags(tags)
                    .register(meterRegistry);
        });
    }


    /**
     * 获取或创建 Timer
     */
    private Timer getOrCreateTimer(String tagKey) {
        return timerCache.computeIfAbsent(tagKey, key -> {
            String[] parts = key.split("\\|");
            Tags tags = Tags.of(
                    "application", parts[0],
                    "ip", parts[1],
                    "desc", parts[2],
                    "method", parts[3],
                    "error_code", parts[4],
                    "type", parts[5]
            );
            return Timer.builder(METRIC_PREFIX + "response_time_seconds")
                    .tags(tags)
                    .register(meterRegistry);
        });
    }

    /**
     * 获取或创建 Gauge
     */
    private Gauge getOrCreateGauge(String tagKey, AtomicInteger concurrentCount) {
        return gaugeCache.computeIfAbsent(tagKey, key -> {
            String[] parts = key.split("\\|");
            Tags tags = Tags.of(
                    "application", parts[0],
                    "ip", parts[1],
                    "desc", parts[2],
                    "method", parts[3],
                    "error_code", parts[4],
                    "type", parts[5]
            );
            return Gauge.builder(METRIC_PREFIX + "concurrent_requests", concurrentCount, AtomicInteger::get)
                    .tags(tags)
                    .register(meterRegistry);
        });
    }

    private String getMethodName(ProceedingJoinPoint joinPoint) {
        MethodSignature signature = (MethodSignature) joinPoint.getSignature();
        Method method = signature.getMethod();
        return method.getDeclaringClass().getSimpleName() + "." + method.getName();
    }

    private String parseErrorCode(Object result) {
        if (result == null) {
            return DEFAULT_TAG_VALUE;
        }

        try {
            if (result instanceof BaseResponse) {
                BaseResponse<?> resp = (BaseResponse<?>) result;
                return String.valueOf(resp.getCode());
            }else {
                return DEFAULT_TAG_VALUE;
            }
        } catch (Throwable t) {
            log.warn("Parse response error code failed", t);
        }
        return DEFAULT_TAG_VALUE;
    }
}

